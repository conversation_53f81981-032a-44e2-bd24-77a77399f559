"use client";

import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { InstagramConnector } from "@/components/oauth/instagram-connector";
import { FacebookConnector } from "@/components/oauth/facebook-connector";
import { YouTubeConnector } from "@/components/oauth/youtube-connector";
import { TikTokConnector } from "@/components/oauth/tiktok-connector";

interface LinkData {
  isValid: boolean;
  isExpired: boolean;
  connection: {
    id: string;
    name: string;
    platform: string;
    isConnected: boolean;
    platformAccountName?: string;
    pendingPageSelection?: boolean;
    availablePages?: any[];
  };
  project: {
    id: string;
    name: string;
  };
  linkName: string;
  description?: string;
  expiresAt?: string;
}

export default function PublicConnectionLinkPage() {
  const params = useParams();
  const token = params.token as string;
  const [linkData, setLinkData] = useState<LinkData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const validateLink = async () => {
      try {
        // Call API to validate the link token and get connection details
        const apiUrl =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:8787";
        console.log(
          "Making request to:",
          `${apiUrl}/public/link/${token}/validate`
        );

        const response = await fetch(`${apiUrl}/public/link/${token}/validate`);

        console.log("Response:", response.status, response.statusText);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error:", response.status, errorText);
          throw new Error("Invalid or expired link");
        }

        const data = await response.json();
        console.log("Link data:", data);
        setLinkData(data);

        if (!data.isValid) {
          toast({
            title: "Invalid Link",
            description: "This connection link is invalid or has expired.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Link validation error:", error);
        toast({
          title: "Link Error",
          description: "Failed to validate the connection link.",
          variant: "destructive",
        });
        setLinkData({
          isValid: false,
          isExpired: true,
          connection: { id: "", name: "", platform: "", isConnected: false },
          project: { id: "", name: "" },
          linkName: "",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (token) {
      validateLink();
    }
  }, [token]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Validating connection link...</p>
        </div>
      </div>
    );
  }

  if (!linkData?.isValid) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Invalid Link</CardTitle>
            <CardDescription>
              This connection link is invalid or has expired.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-muted-foreground mb-4">
              Please contact the person who shared this link for a new one.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  switch (linkData.connection.platform) {
    case "instagram":
    case "instagram_business":
    case "instagram_with_facebook":
      return (
        <InstagramConnector
          connectionId={linkData.connection.id}
          token={token}
        />
      );

    case "facebook":
      return (
        <FacebookConnector
          connectionId={linkData.connection.id}
          token={token}
        />
      );

    case "youtube":
      return (
        <YouTubeConnector connectionId={linkData.connection.id} token={token} />
      );

    case "tiktok":
      return (
        <TikTokConnector connectionId={linkData.connection.id} token={token} />
      );

    default:
      return (
        <div className="max-w-2xl mx-auto text-center space-y-4">
          <h1 className="text-2xl font-bold">Platform Not Supported</h1>
          <p className="text-muted-foreground">
            The platform "{linkData.connection.platform}" is not yet supported
            for OAuth connections.
          </p>
        </div>
      );
  }
}

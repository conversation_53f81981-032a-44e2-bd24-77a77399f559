"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Loader2, Users, CheckCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

interface FacebookPage {
  id: string;
  name: string;
  about?: string;
  fan_count?: number;
  followers_count?: number;
  picture?: {
    data: {
      url: string;
    };
  };
}

interface FacebookPageSelectorPublicProps {
  token: string;
  availablePages: FacebookPage[];
}

export function FacebookPageSelectorPublic({
  token,
  availablePages,
}: FacebookPageSelectorPublicProps) {
  const [pages, setPages] = useState<FacebookPage[]>(availablePages);
  const [currentPageId, setCurrentPageId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [selecting, setSelecting] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Set pages from props
    setPages(availablePages);
    setLoading(false);
  }, [availablePages]);

  const selectPage = async (pageId: string) => {
    setSelecting(pageId);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/public/link/${token}/select-page`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ pageId }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to select Facebook page");
      }

      const data = await response.json();

      toast({
        title: "Success",
        description: `Successfully connected to ${data.selectedPage?.name}`,
      });

      setCurrentPageId(pageId);
      router.push(`/connect/${token}/success`);
    } catch (error) {
      console.error("Error selecting page:", error);
      toast({
        title: "Error",
        description: "Failed to select Facebook page. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSelecting(null);
    }
  };

  const formatFollowerCount = (count?: number) => {
    if (!count) return "0";
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="text-gray-600">Loading your Facebook pages...</span>
      </div>
    );
  }

  if (pages.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
          <svg
            className="w-8 h-8 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          No Facebook Pages Found
        </h3>
        <p className="text-gray-600 max-w-md mx-auto">
          You need to manage at least one Facebook Page to use this integration.
          Please create or get admin access to a Facebook Page and try again.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-4">
        {pages.map((page) => (
          <Card
            key={page.id}
            className={`group transition-all duration-200 hover:shadow-lg border-2 ${
              currentPageId === page.id
                ? "border-blue-500 bg-blue-50/50 shadow-md"
                : "border-gray-200 hover:border-gray-300"
            }`}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="relative">
                    <Avatar className="h-16 w-16 ring-2 ring-white shadow-sm">
                      <AvatarImage
                        src={page.picture?.data?.url}
                        alt={page.name}
                        className="object-cover"
                      />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold text-lg">
                        {page.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    {currentPageId === page.id && (
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-1">
                          {page.name}
                        </h3>
                        {currentPageId === page.id && (
                          <Badge
                            variant="secondary"
                            className="bg-green-100 text-green-800 border-green-200"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Currently Connected
                          </Badge>
                        )}
                      </div>
                    </div>

                    {page.about && (
                      <p className="text-gray-600 text-sm leading-relaxed mb-3 line-clamp-2">
                        {page.about}
                      </p>
                    )}

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">
                          {formatFollowerCount(
                            page.followers_count || page.fan_count
                          )}{" "}
                          followers
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="ml-4 flex-shrink-0">
                  <Button
                    onClick={() => selectPage(page.id)}
                    disabled={selecting === page.id}
                    variant={currentPageId === page.id ? "outline" : "default"}
                    size="lg"
                    className={`min-w-[120px] ${
                      currentPageId === page.id
                        ? "border-green-300 text-green-700 hover:bg-green-50"
                        : "bg-blue-600 hover:bg-blue-700 text-white"
                    }`}
                  >
                    {selecting === page.id ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Connecting...
                      </>
                    ) : currentPageId === page.id ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Connected
                      </>
                    ) : (
                      "Select Page"
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

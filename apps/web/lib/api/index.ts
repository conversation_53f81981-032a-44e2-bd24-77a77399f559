import { hc } from "hono/client";
// Importiere den Typ direkt aus deinem API-Paket!
import type { AppType } from "@socialfeed/api/src/index"; // Passe den Pfad ggf. an

const API_URL = process.env.NEXT_PUBLIC_API_URL as string;

if (!API_URL) {
  throw new Error("NEXT_PUBLIC_API_URL ist nicht gesetzt!");
}

// Diese Funktion muss im Kontext der Propel Auth Provider aufgerufen werden
// damit sie Zugriff auf `getAccessToken` und die `org` Info hat.
export function createApiClient(accessToken: string, orgId: string) {
  const API_URL = process.env.NEXT_PUBLIC_API_URL!;

  return hc<AppType>(API_URL, {
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "X-Org-Id": orgId,
    },
  });
}

export function createPublicApiClient() {
  const API_URL = process.env.NEXT_PUBLIC_API_URL!;

  return hc<AppType>(API_URL);
}

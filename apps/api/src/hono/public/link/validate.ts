import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq, and } from "drizzle-orm";
import { cors } from "hono/cors";

export const validatePublicLink = async (
  c: Context<AppContext, "/public/link/:token/validate">
) => {
  // Add CORS headers for public access
  c.header("Access-Control-Allow-Origin", "*");
  c.header("Access-Control-Allow-Methods", "GET, OPTIONS");
  c.header("Access-Control-Allow-Headers", "Content-Type");

  const token = c.req.param("token");

  console.log("Public link validation request for token:", token);

  if (!token) {
    console.log("No token provided");
    throw new HTTPException(400, { message: "Token is required" });
  }

  const db = getDbClient(c.env.DB);

  try {
    // Get link details with connection and project information
    const linkData = await db
      .select({
        // Link details
        linkId: schema.generatedLinks.id,
        linkName: schema.generatedLinks.name,
        linkDescription: schema.generatedLinks.description,
        linkExpiresAt: schema.generatedLinks.expiresAt,
        linkIsActive: schema.generatedLinks.isActive,

        // Connection details
        connectionId: schema.platformConnections.id,
        connectionName: schema.platformConnections.name,
        connectionPlatform: schema.platformConnections.platform,
        connectionIsConnected: schema.platformConnections.isConnected,
        connectionPlatformAccountName:
          schema.platformConnections.platformAccountName,
        pendingPageSelection: schema.platformConnections.pendingPageSelection,

        // Project details
        projectId: schema.projects.id,
        projectName: schema.projects.name,
      })
      .from(schema.generatedLinks)
      .innerJoin(
        schema.platformConnections,
        eq(
          schema.generatedLinks.platformConnectionId,
          schema.platformConnections.id
        )
      )
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(eq(schema.generatedLinks.token, token))
      .get();

    console.log("Database query result:", linkData);

    if (!linkData) {
      console.log("No link data found for token:", token);
      return c.json(
        {
          isValid: false,
          isExpired: false,
          error: "Link not found",
        },
        404
      );
    }

    // Check if link is active
    if (!linkData.linkIsActive) {
      return c.json(
        {
          isValid: false,
          isExpired: false,
          error: "Link has been deactivated",
        },
        410
      );
    }

    // Check if link has expired
    const now = new Date();
    const isExpired =
      linkData.linkExpiresAt && new Date(linkData.linkExpiresAt) < now;

    if (isExpired) {
      return c.json(
        {
          isValid: false,
          isExpired: true,
          error: "Link has expired",
        },
        410
      );
    }

    // Return valid link data
    return c.json({
      isValid: true,
      isExpired: false,
      connection: {
        id: linkData.connectionId,
        name: linkData.connectionName,
        platform: linkData.connectionPlatform,
        isConnected: linkData.connectionIsConnected,
        platformAccountName: linkData.connectionPlatformAccountName,
        pendingPageSelection: linkData.pendingPageSelection,
      },
      project: {
        id: linkData.projectId,
        name: linkData.projectName,
      },
      linkName: linkData.linkName,
      description: linkData.linkDescription,
      expiresAt: linkData.linkExpiresAt,
    });
  } catch (error) {
    console.error("Link validation error:", error);
    throw new HTTPException(500, { message: "Failed to validate link" });
  }
};

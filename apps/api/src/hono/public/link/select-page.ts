import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq, and } from "drizzle-orm";
import { decryptToken, encryptToken } from "../../../token-utils";
import { triggerInitialSync } from "../../../sync-utils";
import { z } from "zod";
import { zValidator } from "@hono/zod-validator";

const selectPageValidator = z.object({
  pageId: z.string().min(1, "Page ID is required"),
});

export const selectPageBodyValidator = zValidator("json", selectPageValidator);

export const selectFacebookPagePublic = async (
  c: Context<
    AppContext,
    "/public/link/:token/select-page",
    {
      in: {
        json: z.infer<typeof selectPageValidator>;
      };
      out: {
        json: any;
      };
    }
  >
) => {
  // Add CORS headers for public access
  c.header("Access-Control-Allow-Origin", "*");
  c.header("Access-Control-Allow-Methods", "POST, OPTIONS");
  c.header("Access-Control-Allow-Headers", "Content-Type");

  const token = c.req.param("token");
  const { pageId } = c.req.valid("json");

  if (!token) {
    throw new HTTPException(400, { message: "Token is required" });
  }

  const db = getDbClient(c.env.DB);

  try {
    // Get link and connection details
    const linkData = await db
      .select({
        // Link details
        linkId: schema.generatedLinks.id,
        linkIsActive: schema.generatedLinks.isActive,
        linkExpiresAt: schema.generatedLinks.expiresAt,

        // Connection details
        connectionId: schema.platformConnections.id,
        connectionPlatform: schema.platformConnections.platform,
        connectionPendingPageSelection:
          schema.platformConnections.pendingPageSelection,
        connectionAccessTokenEncrypted:
          schema.platformConnections.accessTokenEncrypted,
        availablePages: schema.platformConnections.availablePages,

        // Project details
        projectId: schema.projects.id,
      })
      .from(schema.generatedLinks)
      .innerJoin(
        schema.platformConnections,
        eq(
          schema.generatedLinks.platformConnectionId,
          schema.platformConnections.id
        )
      )
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(eq(schema.generatedLinks.token, token))
      .get();

    if (!linkData) {
      throw new HTTPException(404, {
        message: "Invalid or expired link token",
      });
    }

    // Check if link is active
    if (!linkData.linkIsActive) {
      throw new HTTPException(410, { message: "Link has been deactivated" });
    }

    // Check if link has expired
    const now = new Date();
    const isExpired =
      linkData.linkExpiresAt && new Date(linkData.linkExpiresAt) < now;

    if (isExpired) {
      throw new HTTPException(410, { message: "Link has expired" });
    }

    // Verify this is a Facebook connection
    if (linkData.connectionPlatform !== "facebook") {
      throw new HTTPException(400, {
        message: "This endpoint is only for Facebook connections",
      });
    }

    // Verify connection is pending page selection
    if (!linkData.connectionPendingPageSelection) {
      throw new HTTPException(400, {
        message: "This connection is not pending page selection",
      });
    }

    if (!linkData.connectionAccessTokenEncrypted) {
      throw new HTTPException(400, {
        message: "No access token found for this connection",
      });
    }

    // Get the user's access token
    const userAccessToken = await decryptToken(
      linkData.connectionAccessTokenEncrypted,
      c.env.ENCRYPTION_KEY
    );

    if (!userAccessToken) {
      throw new HTTPException(400, {
        message: "Failed to decrypt access token",
      });
    }

    // Fetch pages with current token to get the selected page details
    const pagesResponse = await fetch(
      `https://graph.facebook.com/v19.0/me/accounts?fields=id,name,about,fan_count,followers_count,picture.type(large),access_token&access_token=${userAccessToken}`
    );

    if (!pagesResponse.ok) {
      const errorText = await pagesResponse.text();
      console.error("Failed to fetch user pages:", errorText);
      throw new HTTPException(400, {
        message: "Failed to fetch Facebook pages. Please try reconnecting.",
      });
    }

    const pagesData = (await pagesResponse.json()) as { data?: any[] };
    const selectedPage = pagesData.data?.find(
      (page: any) => page.id === pageId
    );

    if (!selectedPage || !selectedPage.access_token) {
      throw new HTTPException(400, {
        message: "Page not found or no access token available for this page",
      });
    }

    // Encrypt the page access token
    const encryptedPageAccessToken = await encryptToken(
      selectedPage.access_token,
      c.env.ENCRYPTION_KEY
    );

    // Update connection with selected page and page access token
    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: selectedPage.id,
        platformAccountName: selectedPage.name,
        platformAccountDescription: selectedPage.about,
        platformAccountFollowers:
          selectedPage.followers_count || selectedPage.fan_count,
        platformAccountProfilePictureUrl: selectedPage.picture?.data?.url,
        accessTokenEncrypted: encryptedPageAccessToken, // Store page access token instead of user token
        tokenExpiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // Page tokens last 60 days
        isConnected: true, // Now fully connected
        pendingPageSelection: false,
        availablePages: null, // Clear the available pages data
        lastCheckedAt: new Date(),
      })
      .where(eq(schema.platformConnections.id, linkData.connectionId));

    // Trigger initial sync now that page is selected
    await triggerInitialSync(c.env, linkData.connectionId);

    return c.json({
      success: true,
      message: "Facebook page selected successfully",
      selectedPage: {
        id: selectedPage.id,
        name: selectedPage.name,
        description: selectedPage.about,
        followers_count: selectedPage.followers_count || selectedPage.fan_count,
        profile_picture_url: selectedPage.picture?.data?.url,
      },
    });
  } catch (e) {
    console.error(`Failed to select Facebook page for token ${token}:`, e);

    if (e instanceof HTTPException) {
      throw e;
    }

    throw new HTTPException(500, {
      message: "Failed to select Facebook page",
    });
  }
};

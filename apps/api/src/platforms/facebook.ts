import { z } from "zod";
import { fetchWithRetry } from "../graph-api-shared";
import { Bindings, GraphApiPagingSchema } from "../types";

import { logErrorToAnalytics } from "../analytics-utils";
import * as constants from "../constants";
import { decryptToken, encryptToken } from "../token-utils";
import { Post, PlatformConnection, InsertPost } from "../types";
import { PlatformInformation } from ".";
import { HTTPException } from "hono/http-exception";
import { DateTime } from "luxon";
import { getDbClient } from "../database-service";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { eq } from "drizzle-orm";

const FacebookTokenResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string().optional(),
  expires_in: z.number().optional(),
});

const FacebookPostSchema = z.object({
  id: z.string(),
  message: z.string().optional(),
  created_time: z.string(),
  permalink_url: z.string().optional(),
  type: z.string().optional(),
  likes: z
    .object({
      summary: z.object({
        total_count: z.number(),
      }),
    })
    .optional(),
  comments: z
    .object({
      summary: z.object({
        total_count: z.number(),
      }),
    })
    .optional(),
});

export async function checkFacebookToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const testUrl = `https://graph.facebook.com/v18.0/me?fields=id&access_token=${accessToken}`;
  try {
    const response = await fetch(testUrl);
    if (!response.ok) return false;
    const jsonResp = (await response.json()) as any;
    return jsonResp.id ? true : false;
  } catch {
    return false;
  }
}

export async function refreshFacebookToken(
  connection: PlatformConnection,
  env: Bindings
): Promise<{ accessToken: string; expiresAt: Date }> {
  // Facebook long-lived tokens last 60 days and cannot be refreshed
  // Instead, we need to check if the token is still valid and extend it if possible
  if (!connection.accessTokenEncrypted) {
    throw new Error("Connection does not have an encrypted access token");
  }

  const decryptedToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!decryptedToken) {
    throw new Error("Failed to decrypt access token");
  }

  // Try to extend the token using the fb_exchange_token method
  const url = `https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&client_id=${env.FACEBOOK_APP_ID}&client_secret=${env.FACEBOOK_APP_SECRET}&fb_exchange_token=${decryptedToken}`;
  const response = await fetch(url);

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Facebook token extension failed:", errorText);
    throw new Error(
      "Facebook token cannot be refreshed. Please reconnect your account."
    );
  }

  const data = (await response.json()) as {
    access_token: string;
    expires_in?: number;
    error?: any;
  };

  if (data.error) {
    throw new Error(`Failed to extend Facebook token: ${data.error.message}`);
  }

  const newAccessToken = data.access_token;
  const newExpiresIn = data.expires_in;
  const newExpiresAt = new Date(
    Date.now() + (newExpiresIn ?? 60 * 60 * 24 * 60) * 1000 // Default to 60 days
  );

  const encryptedToken = await encryptToken(newAccessToken, env.ENCRYPTION_KEY);
  if (!encryptedToken) {
    throw new Error("Failed to encrypt new access token");
  }

  const db = getDbClient(env.DB);
  await db
    .update(schema.platformConnections)
    .set({
      accessTokenEncrypted: encryptedToken,
      tokenExpiresAt: newExpiresAt,
      lastCheckedAt: new Date(),
      needsReconnect: false,
      hasError: false,
    })
    .where(eq(schema.platformConnections.id, connection.id));

  return { accessToken: newAccessToken, expiresAt: newExpiresAt };
}

export async function exchangeFacebookToken(
  authorizationCode: string,
  env: Bindings
): Promise<{ accessToken: string; expiresAt: Date }> {
  console.log("Facebook: Exchanging authorization code...", authorizationCode);

  // --- Step 1: Exchange authorization code for a short-lived access token ---
  const tokenUrl = `https://graph.facebook.com/oauth/access_token`;
  const body = new URLSearchParams();
  body.append("client_id", env.FACEBOOK_APP_ID);
  body.append("client_secret", env.FACEBOOK_APP_SECRET);
  body.append("grant_type", "authorization_code");
  body.append("redirect_uri", `${env.NEXT_PUBLIC_API_URL}/oauth/meta/callback`);
  body.append("code", authorizationCode);

  const shortLivedResponse = await fetch(`${tokenUrl}?${body.toString()}`);

  if (!shortLivedResponse.ok) {
    const errorText = await shortLivedResponse.text();
    console.error("Facebook short-lived token exchange failed:", errorText);
    throw new HTTPException(400, {
      message: "Short-lived token exchange failed",
    });
  }

  const shortLivedData = await shortLivedResponse.json();
  console.log("Facebook short-lived token data:", shortLivedData);
  const shortLivedValidation =
    FacebookTokenResponseSchema.safeParse(shortLivedData);

  if (!shortLivedValidation.success) {
    console.error(
      "Invalid short-lived token response:",
      shortLivedValidation.error
    );
    throw new HTTPException(400, {
      message: "Invalid short-lived token response",
    });
  }

  const shortLivedAccessToken = shortLivedValidation.data.access_token;

  // --- Step 2: Exchange the short-lived token for a long-lived token ---
  const longLivedUrl = `https://graph.facebook.com/oauth/access_token`;
  const longLivedBody = new URLSearchParams();
  longLivedBody.append("grant_type", "fb_exchange_token");
  longLivedBody.append("client_id", env.FACEBOOK_APP_ID);
  longLivedBody.append("client_secret", env.FACEBOOK_APP_SECRET);
  longLivedBody.append("fb_exchange_token", shortLivedAccessToken);

  const finalResponse = await fetch(
    `${longLivedUrl}?${longLivedBody.toString()}`
  );

  console.log("Facebook long-lived token exchange response:", finalResponse);

  if (!finalResponse.ok) {
    const errorText = await finalResponse.text();
    console.error(`Facebook long-lived token exchange failed:`, errorText);
    throw new HTTPException(400, {
      message: "Long-lived token exchange failed",
    });
  }

  console.log("Facebook long-lived token exchange successful");

  const longLivedData = await finalResponse.json();
  console.log("Facebook long-lived token data:", longLivedData);
  const longLivedValidation =
    FacebookTokenResponseSchema.safeParse(longLivedData);

  if (!longLivedValidation.success) {
    console.error(
      "Invalid long-lived token response:",
      longLivedValidation.error
    );
    throw new HTTPException(400, {
      message: "Invalid long-lived token response",
    });
  }

  return {
    accessToken: longLivedValidation.data.access_token,
    expiresAt: DateTime.now()
      .plus({
        seconds: longLivedValidation.data.expires_in,
      })
      .toJSDate(),
  };
}

export const getFacebookPages = async (accessToken: string, env: Bindings) => {
  const pagesResponse = await fetch(
    `https://graph.facebook.com/v19.0/me/accounts?fields=id,name,about,fan_count,picture.type(large),followers_count,access_token&access_token=${accessToken}`
  );

  if (!pagesResponse.ok) {
    const errorText = await pagesResponse.text();
    console.error("Failed to get user pages from graph", errorText);
    throw new HTTPException(400, {
      message:
        "Failed to get user pages. Please make sure you manage at least one Facebook Page.",
    });
  }

  const pagesData = await pagesResponse.json();

  const FacebookPagesResponseSchema = z.object({
    data: z.array(
      z.object({
        id: z.string(),
        name: z.string(),
        about: z.string().optional(),
        fan_count: z.number().optional(),
        followers_count: z.number().optional(),
        access_token: z.string(), // Page access token
        picture: z
          .object({
            data: z.object({
              url: z.string().url(),
            }),
          })
          .optional(),
      })
    ),
  });

  const pagesValidation = FacebookPagesResponseSchema.safeParse(pagesData);

  if (!pagesValidation.success) {
    console.error("Invalid pages response:", pagesValidation.error);
    throw new HTTPException(400, { message: "Invalid pages response" });
  }

  return pagesValidation.data.data;
};

export const getPlatformInformation = async (
  connection: PlatformConnection,
  env: Bindings
): Promise<PlatformInformation> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }

  // If this connection is pending page selection, we can't provide platform info yet
  if (connection.pendingPageSelection) {
    throw new HTTPException(400, {
      message:
        "Page selection is required before accessing platform information",
    });
  }

  if (!connection.platformAccountId) {
    throw new HTTPException(400, {
      message: "No Facebook Page selected for this connection",
    });
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );

  // Get the specific page information
  const pageResponse = await fetch(
    `https://graph.facebook.com/v19.0/${connection.platformAccountId}?fields=id,name,about,fan_count,picture.type(large),followers_count&access_token=${accessToken}`
  );

  if (!pageResponse.ok) {
    const errorText = await pageResponse.text();
    console.error("Failed to get page info from graph", errorText);
    throw new HTTPException(400, { message: "Failed to get page information" });
  }

  const pageData = await pageResponse.json();
  const FacebookPageSchema = z.object({
    id: z.string(),
    name: z.string(),
    about: z.string().optional(),
    fan_count: z.number().optional(),
    followers_count: z.number().optional(),
    picture: z
      .object({
        data: z.object({
          url: z.string().url(),
        }),
      })
      .optional(),
  });

  const pageValidation = FacebookPageSchema.safeParse(pageData);

  if (!pageValidation.success) {
    console.error("Invalid page response:", pageValidation.error);
    throw new HTTPException(400, { message: "Invalid page response" });
  }

  const page = pageValidation.data;
  return {
    id: page.id,
    name: page.name,
    description: page.about,
    followers_count: page.followers_count || page.fan_count,
    posts_count: undefined, // Not available directly
    profile_picture_url: page.picture?.data.url,
  };
};

type FacebookPost = z.infer<typeof FacebookPostSchema>;

export async function fetchLatestFacebookGraphMedia(
  accessToken: string,
  platformAccountId: string, // FB Page ID
  env: Bindings,
  limit?: number
): Promise<{ posts: FacebookPost[]; error?: any } | null> {
  const contextInfo = `Latest Graph Media for ${platformAccountId}`;
  const collectedPosts: FacebookPost[] = [];
  const effectiveLimit = limit ?? Number.MAX_SAFE_INTEGER;
  const fetchLimitPerPage = 50; // Set a constant page size
  const fields =
    "id,message,likes.summary(true),comments.summary(true),created_time,permalink_url,type";
  let url: string | null =
    `https://graph.facebook.com/${constants.META_API_VERSION}/${platformAccountId}/posts?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;

  try {
    while (url && collectedPosts.length < effectiveLimit) {
      const remainingLimit = effectiveLimit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(url);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph media page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      );
      const pageJson = await response.json();

      const pageValidation = z
        .object({
          data: z.array(FacebookPostSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "GRAPH_MEDIA_PAGE_VALIDATION_ERROR",
          "Invalid graph media page response",
          { errors: pageValidation.error.flatten(), received: pageJson }
        );
        break;
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedPosts.push(...pageData.data);
      } else {
        break;
      }
      if (collectedPosts.length >= effectiveLimit) break;

      url = pageData.paging?.next || null;
      if (url)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }

    console.log(
      `GRAPH_API: Fetched ${collectedPosts.length} graph posts for ${platformAccountId}.`
    );
    return { posts: collectedPosts.slice(0, effectiveLimit) };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch latest graph media for ${platformAccountId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { platformAccountId, error: String(error) }
    );
    return {
      posts: [],
      error: error,
    };
  }
}

export const getPosts = async (
  connection: PlatformConnection,
  { limit, cursor }: { limit?: number; cursor?: string },
  env: Bindings
): Promise<{ posts: Post[]; nextCursor?: string }> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }
  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Facebook access token");
  }

  const mediaResult = await fetchLatestFacebookGraphMedia(
    accessToken,
    connection.platformAccountId!,
    env,
    limit ?? connection.postLimit ?? undefined
  );

  if (!mediaResult) {
    throw new Error(`Meta API client failed`);
  }
  if (mediaResult.error) {
    throw mediaResult.error;
  }

  return {
    posts: mediaResult.posts as any[], // Return raw Facebook posts
    nextCursor: undefined, // TODO: Implement cursor logic
  };
};

export const getPost = async (
  connection: PlatformConnection,
  postId: string,
  env: Bindings
): Promise<any | null> => {
  if (!connection.accessTokenEncrypted) {
    throw new Error("Missing access token in connection");
  }

  const accessToken = await decryptToken(
    connection.accessTokenEncrypted,
    env.ENCRYPTION_KEY
  );
  if (!accessToken) {
    throw new Error("Failed to decrypt Facebook access token");
  }

  const fields =
    "id,message,likes.summary(true),comments.summary(true),created_time,permalink_url,type";
  const url = `https://graph.facebook.com/${constants.META_API_VERSION}/${postId}?fields=${fields}&access_token=${accessToken}`;
  const response = await fetch(url);
  if (!response.ok) {
    return null;
  }
  const postData = await response.json();
  const postValidation = FacebookPostSchema.safeParse(postData);

  if (!postValidation.success) {
    return null;
  }

  return postValidation.data;
};

export function mapFacebookPostData(
  post: FacebookPost,
  connection: PlatformConnection
): InsertPost {
  return {
    platformConnectionId: connection.id,
    mediaId: post.id,
    platform: "facebook",
    likeCount: post.likes?.summary?.total_count ?? 0,
    commentsCount: post.comments?.summary?.total_count ?? 0,
    caption: post.message ?? null,
    mediaUrl: null, // Media URL not available without full_picture field
    mediaType:
      post.type === "photo"
        ? "IMAGE"
        : post.type === "video"
          ? "VIDEO"
          : "UNKNOWN",
    permalink: post.permalink_url ?? null,
    timestamp: post.created_time ? new Date(post.created_time) : new Date(),
    lastFetched: new Date(),
    lastWebhookUpdate: null,
    engagement: {},
  };
}
